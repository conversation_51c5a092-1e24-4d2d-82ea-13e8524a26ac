.modal-operation {
  text-align: right;
  margin-top: 20px;
}

.add-task-moduel {
  :global(.ant-modal-body) {
    padding: 0 0 0 10px;
  }
}
.yth-inspection-moduel {
  position: relative;
  height: calc(100vh - 180px); // 弹窗高度：屏幕高度减去上下各50px留白
  overflow-y: auto; // 允许垂直滚动
  overflow-x: hidden; // 隐藏水平滚动
  padding-right: 10px; // 为滚动条留出空间
  box-sizing: border-box; // 确保padding包含在高度计算中
  margin-top: 10px;
}

.drawer-filter-operation {
  position: absolute;
  bottom: 20px; // 距离弹窗底部20px
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 100; // 相对于弹窗内容的层级
  align-items: center;
  background: rgba(255, 255, 255, 0.95); // 添加半透明背景
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); // 添加阴影效果
}

// 按钮样式通过 gap 和 flex 布局处理，无需额外样式
