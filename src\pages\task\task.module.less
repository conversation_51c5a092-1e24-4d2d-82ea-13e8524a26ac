.modal-operation {
  text-align: right;
  margin-top: 20px;
}

.yth-inspection-moduel {
  position: relative;
  height: calc(100vh - 100px); // 屏幕高度减去上下各50px
  overflow-y: auto; // 允许垂直滚动
  padding-right: 10px; // 为滚动条留出空间
}

.drawer-filter-operation {
  position: fixed;
  bottom: 70px; // 距离屏幕底部70px，留出空间
  right: 30px;
  display: flex;
  gap: 10px;
  z-index: 1000; // 提高层级确保在最上层
  align-items: center;
  background: rgba(255, 255, 255, 0.9); // 添加半透明背景
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); // 添加阴影效果
}

// 按钮样式通过 gap 和 flex 布局处理，无需额外样式
