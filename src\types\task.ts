import { PageQueryParam, PageResponse } from '@/types/common';

/**
 * 任务检查点信息返回结果
 */
export interface TaskPointsVo {
  /** 主键 */
  id?: string;
  /** 任务id */
  taskId?: string;
  /** 检查点名称 */
  pointName?: string;
  /** 检查点坐标 */
  pointLocation?: string;
  /** 巡检内容 */
  inspectionContent?: string;
  /** 检查状态（0: 未检查，1: 已检查） */
  checkStatus?: number;
  /** 检查结果（0: 正常，1: 异常） */
  checkResult?: number;
  /** 结果描述 */
  checkResultDescription?: string;
  /** 结果照片（图片URL） */
  attachments?: string;
  /** 创建时间 */
  createDate?: string;
  /** 更新时间 */
  updateDate?: string;
}
/**
 * 巡检任务设备信息
 */
export interface TaskDeviceVo {
  /** 主键 */
  id?: string;
  /** 任务id */
  taskId?: string;
  /** 设备类型 */
  deviceType?: string;
  /** 设备id */
  deviceId?: string;
  /** 设备编码 */
  deviceCode?: string;
  /** 设备名称 */
  deviceName?: string;
  /** 备注 */
  remark?: string;
}
/**
 * 巡检任务信息
 */
export interface TaskVo {
  /** 主键 */
  id?: string;
  /** 任务编码 */
  taskCode?: string;
  /** 任务名称 */
  taskName?: string;
  /** 计划id */
  planId?: string;
  /** 计划名称 */
  planName?: string;
  /** 巡检方式 */
  inspectionMethod?: string | { code?: string; text?: string; id?: string }[];
  /** 任务类型 */
  taskType?: string | { code?: string; text?: string; id?: string }[];
  /** 负责人id */
  directorUserId?: string;
  /** 负责人名称 */
  directorUserName?: string;
  /** 负责人电话 */
  directorPhone?: string;
  /** 任务状态 */
  taskStatus?: string | { code?: string; text?: string; id?: string };
  /** 计划开始时间 */
  planStartTime?: string;
  /** 计划结束时间 */
  planEndTime?: string;
  /** 实际开始时间 */
  actualStartTime?: string;
  /** 实际结束时间 */
  actualEndTime?: string;
  /** 备注 */
  remark?: string;
  /** 参与巡检人 */
  partyUserIds?: string;
  /** 创建时间 */
  createDate?: string;
  /** 更新时间 */
  updateDate?: string;
  /** 是否发送短信提醒 (0: 不发送, 1: 需要发送) */
  isRemind?: number | { code?: string; text?: string; id?: string };
  /** 任务检查点列表 */
  taskPointsList?: TaskPointsVo[];
  /** 任务设备列表 */
  taskDevicesList?: TaskDeviceVo[];
}
/**
 * 计划检查点查询参数
 */
export interface TaskQueryParam {
  TaskName?: string;
  inspectionMethod?: string;
}

/**
 * @description 新增参数
 * @param data 新增参数
 */
export interface TaskInsertParam extends TaskVo {
  id: string;
}
/**
 * @description 更新参数
 * @param data 更新参数
 */
export interface TaskUpdateParam extends TaskVo {
  id: string;
}
/**
 * @description 分页返回结果
 * @param data 分页查询参数
 */
export interface TaskPageResponse extends PageResponse<TaskVo> {
  data: TaskVo[];
}

/**
 * @description 分页查询参数
 * @param data 分页查询参数
 */
export interface TaskPageQueryParam extends PageQueryParam<TaskQueryParam> {
  condition: TaskQueryParam;
}
